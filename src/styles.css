/* Global Styles for Family Hub App */

/* CSS Reset */
*, *::before, *::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

/* Base Styles */
html, body {
  height: 100%;
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
  line-height: 1.6;
  color: #2c3e50;
  background-color: #f0f2f5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

app-root {
  display: block;
  height: 100%;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  line-height: 1.3;
  margin-bottom: 0.5rem;
  color: #2c3e50;
}

p {
  margin-bottom: 1rem;
}

a {
  color: #3498db;
  text-decoration: none;
  transition: color 0.3s ease;
}

a:hover {
  color: #2980b9;
}

/* Layout Classes */
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-center {
  justify-content: center;
}

.justify-end {
  justify-content: flex-end;
}

.flex-grow {
  flex-grow: 1;
}

.grid {
  display: grid;
}

.gap-md {
  gap: 1.5rem;
}

/* Utility Classes */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.text-center {
  text-align: center;
}

.rounded {
  border-radius: 8px;
}

.rounded-full {
  border-radius: 50%;
}

.shadow {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.shadow-sm {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.mb-1 {
  margin-bottom: 0.25rem;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.mb-3 {
  margin-bottom: 1rem;
}

.mb-4 {
  margin-bottom: 1.5rem;
}

.mb-6 {
  margin-bottom: 2.5rem;
}

.mb-8 {
  margin-bottom: 3rem;
}

.mr-2 {
  margin-right: 0.5rem;
}

.mr-3 {
  margin-right: 1rem;
}

.mr-4 {
  margin-right: 1.5rem;
}

.p-4 {
  padding: 1rem;
}

.p-6 {
  padding: 1.5rem;
}

.p-8 {
  padding: 2rem;
}

.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.py-3 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}

.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}

/* Colors */
.bg-white {
  background-color: white;
}

.bg-light {
  background-color: #f8f9fa;
}

.bg-primary {
  background-color: #3498db;
}

.bg-success {
  background-color: #2ecc71;
}

.text-white {
  color: white;
}

.text-dark {
  color: #2c3e50;
}

.text-gray {
  color: #5a6c7d;
}

.text-primary {
  color: #3498db;
}

.text-success {
  color: #2ecc71;
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  text-align: center;
  min-width: 140px;
}

.btn-primary {
  background-color: #3498db;
  color: white;
  box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.btn-primary:hover {
  background-color: #2980b9;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);
}

.btn-secondary {
  background-color: transparent;
  color: #3498db;
  border: 2px solid #3498db;
}

.btn-secondary:hover {
  background-color: #3498db;
  color: white;
  transform: translateY(-2px);
}

.btn-large {
  padding: 1rem 2rem;
  font-size: 1.1rem;
}

/* Custom scrollbar for sidebar if content overflows */
.sidebar::-webkit-scrollbar {
  width: 8px;
}

.sidebar::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.sidebar::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

.sidebar::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* Responsive utilities */
@media (min-width: 768px) {
  .md-grid-cols-2 {
    grid-template-columns: repeat(2, 1fr);
  }

  .md-rounded-none {
    border-radius: 0;
  }

  .md-p-8 {
    padding: 2rem;
  }
}

@media (min-width: 1024px) {
  .lg-grid-cols-3 {
    grid-template-columns: repeat(3, 1fr);
  }

  .lg-grid-cols-4 {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* Focus Styles for Accessibility */
button:focus,
a:focus,
input:focus,
textarea:focus,
select:focus {
  outline: 2px solid #3498db;
  outline-offset: 2px;
}

/* Icons */
.icon {
  display: inline-block;
  font-size: 1.5rem;
  line-height: 1;
  vertical-align: middle;
}

.icon-lg {
  font-size: 2rem;
}

.icon-xl {
  font-size: 3rem;
}

.icon-2xl {
  font-size: 4rem;
}

/* Print Styles */
@media print {
  * {
    background: transparent !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }

  a, a:visited {
    text-decoration: underline;
  }

  a[href]:after {
    content: " (" attr(href) ")";
  }

  abbr[title]:after {
    content: " (" attr(title) ")";
  }

  .no-print {
    display: none !important;
  }
}
/* Additional styles for landing page compatibility */
html, body {
  height: 100%;
  margin: 0;
  padding: 0;
}

app-root {
  display: block;
  height: 100%;
}
