import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-landing',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="app-container">
      <!-- Sidebar -->
      <aside class="sidebar">
        <div class="sidebar-header">
          <div class="logo">
            <span class="home-icon">🏠</span>
            <span class="logo-text">Family Hub</span>
          </div>
        </div>
        
        <nav class="sidebar-nav">
          <div class="nav-item active">
            <span class="nav-icon">📊</span>
            <span class="nav-text">Dashboard</span>
          </div>
          <div class="nav-item">
            <span class="nav-icon">📅</span>
            <span class="nav-text">Calendar</span>
          </div>
          <div class="nav-item">
            <span class="nav-icon">📱</span>
            <span class="nav-text">Media</span>
          </div>
          <div class="nav-item">
            <span class="nav-icon">📁</span>
            <span class="nav-text">Files</span>
          </div>
          <div class="nav-item">
            <span class="nav-icon">🔗</span>
            <span class="nav-text">Links</span>
          </div>
          <div class="nav-item">
            <span class="nav-icon">📖</span>
            <span class="nav-text">Journal</span>
          </div>
          <div class="nav-item">
            <span class="nav-icon">✉️</span>
            <span class="nav-text">Invitations</span>
          </div>
        </nav>
      </aside>

      <!-- Main Content -->
      <main class="main-content">
        <div class="content-wrapper">
          <!-- Header Section -->
          <section class="welcome-section">
            <h1 class="welcome-title">Welcome to Family Hub!</h1>
            <p class="welcome-subtitle">Your central place for family organization, connection, and cherished memories.</p>
            <p class="welcome-description">Navigate using the sidebar or explore the features below. We're thrilled to have you and your family on board.</p>
          </section>

          <!-- Feature Cards Grid -->
          <section class="features-grid">
            <div class="feature-card">
              <div class="feature-icon">📅</div>
              <div class="feature-content">
                <h3 class="feature-title">Family Calendar</h3>
                <p class="feature-description">View and manage shared events.</p>
                <button class="feature-button">Open Calendar →</button>
              </div>
            </div>

            <div class="feature-card">
              <div class="feature-icon">📱</div>
              <div class="feature-content">
                <h3 class="feature-title">Media Sharing</h3>
                <p class="feature-description">Share photos and videos.</p>
                <button class="feature-button">View Media →</button>
              </div>
            </div>

            <div class="feature-card">
              <div class="feature-icon">📁</div>
              <div class="feature-content">
                <h3 class="feature-title">File Vault</h3>
                <p class="feature-description">Store important documents securely.</p>
                <button class="feature-button">Open Files →</button>
              </div>
            </div>

            <div class="feature-card">
              <div class="feature-icon">📝</div>
              <div class="feature-content">
                <h3 class="feature-title">Shared Lists</h3>
                <p class="feature-description">Collaborate on to-do and shopping lists.</p>
                <button class="feature-button">Manage Lists →</button>
              </div>
            </div>

            <div class="feature-card">
              <div class="feature-icon">📖</div>
              <div class="feature-content">
                <h3 class="feature-title">Family Journal</h3>
                <p class="feature-description">Share updates and milestones.</p>
                <button class="feature-button">Read Journal →</button>
              </div>
            </div>

            <div class="feature-card">
              <div class="feature-icon">✉️</div>
              <div class="feature-content">
                <h3 class="feature-title">AI Invitations</h3>
                <p class="feature-description">Create event invitations with AI.</p>
                <button class="feature-button">Create Invitation →</button>
              </div>
            </div>
          </section>

          <!-- Quick Stats Section -->
          <section class="quick-stats">
            <h2 class="stats-title">Quick Stats</h2>
            <div class="stats-grid">
              <div class="stat-card">
                <div class="stat-number">3</div>
                <div class="stat-label">Upcoming Events</div>
                <div class="stat-sublabel">this week</div>
              </div>
              <div class="stat-card">
                <div class="stat-number">12</div>
                <div class="stat-label">New Photos</div>
                <div class="stat-sublabel">last 7 days</div>
              </div>
              <div class="stat-card">
                <div class="stat-number">5</div>
                <div class="stat-label">Shared Files</div>
                <div class="stat-sublabel">total</div>
              </div>
              <div class="stat-card">
                <div class="stat-number">2</div>
                <div class="stat-label">Active Lists</div>
                <div class="stat-sublabel">pending items</div>
              </div>
            </div>
          </section>
        </div>
      </main>
    </div>
  `,
  styles: [`
    .app-container {
      display: flex;
      min-height: 100vh;
      background-color: #f5f5f5;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }

    /* Sidebar Styles */
    .sidebar {
      width: 240px;
      background-color: #ffffff;
      border-right: 1px solid #e0e0e0;
      display: flex;
      flex-direction: column;
    }

    .sidebar-header {
      padding: 20px;
      border-bottom: 1px solid #e0e0e0;
    }

    .logo {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .home-icon {
      font-size: 20px;
    }

    .logo-text {
      font-size: 18px;
      font-weight: 600;
      color: #333;
    }

    .sidebar-nav {
      padding: 20px 0;
      flex: 1;
    }

    .nav-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 12px 20px;
      cursor: pointer;
      transition: background-color 0.2s;
    }

    .nav-item:hover {
      background-color: #f0f0f0;
    }

    .nav-item.active {
      background-color: #e8f5e8;
      border-right: 3px solid #4caf50;
    }

    .nav-icon {
      font-size: 16px;
      width: 20px;
      text-align: center;
    }

    .nav-text {
      font-size: 14px;
      color: #333;
      font-weight: 500;
    }

    /* Main Content Styles */
    .main-content {
      flex: 1;
      overflow-y: auto;
    }

    .content-wrapper {
      max-width: 1200px;
      margin: 0 auto;
      padding: 40px 20px;
    }

    /* Welcome Section */
    .welcome-section {
      margin-bottom: 40px;
    }

    .welcome-title {
      font-size: 32px;
      font-weight: 700;
      color: #333;
      margin: 0 0 16px 0;
    }

    .welcome-subtitle {
      font-size: 16px;
      color: #666;
      margin: 0 0 8px 0;
      line-height: 1.5;
    }

    .welcome-description {
      font-size: 14px;
      color: #888;
      margin: 0;
      line-height: 1.5;
    }

    /* Features Grid */
    .features-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 20px;
      margin-bottom: 40px;
    }

    .feature-card {
      background: white;
      border-radius: 8px;
      padding: 24px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      border: 1px solid #e0e0e0;
    }

    .feature-icon {
      font-size: 24px;
      margin-bottom: 16px;
    }

    .feature-title {
      font-size: 18px;
      font-weight: 600;
      color: #333;
      margin: 0 0 8px 0;
    }

    .feature-description {
      font-size: 14px;
      color: #666;
      margin: 0 0 16px 0;
      line-height: 1.4;
    }

    .feature-button {
      background: #4a90e2;
      color: white;
      border: none;
      padding: 10px 16px;
      border-radius: 6px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: background-color 0.2s;
      width: 100%;
    }

    .feature-button:hover {
      background: #357abd;
    }

    /* Quick Stats Section */
    .quick-stats {
      background: white;
      border-radius: 8px;
      padding: 24px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      border: 1px solid #e0e0e0;
    }

    .stats-title {
      font-size: 20px;
      font-weight: 600;
      color: #333;
      margin: 0 0 20px 0;
    }

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 20px;
    }

    .stat-card {
      text-align: center;
      padding: 16px;
    }

    .stat-number {
      font-size: 32px;
      font-weight: 700;
      color: #333;
      margin-bottom: 8px;
    }

    .stat-label {
      font-size: 14px;
      font-weight: 500;
      color: #333;
      margin-bottom: 4px;
    }

    .stat-sublabel {
      font-size: 12px;
      color: #888;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
      .app-container {
        flex-direction: column;
      }

      .sidebar {
        width: 100%;
        order: 2;
      }

      .features-grid {
        grid-template-columns: 1fr;
      }

      .stats-grid {
        grid-template-columns: repeat(2, 1fr);
      }
    }

    @media (max-width: 1024px) {
      .features-grid {
        grid-template-columns: repeat(2, 1fr);
      }
    }
  `]
})
export class LandingComponent {
}
