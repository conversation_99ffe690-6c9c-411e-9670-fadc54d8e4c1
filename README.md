# FamilyAppUi
# Family Hub

A secure and private platform designed to help families stay connected, share memories, and coordinate activities together.

## Features

- **Family Calendar**: View and manage shared events
- **Media Sharing**: Share photos and videos with family members
- **File Vault**: Store important documents securely
- **Shared Lists**: Collaborate on to-do and shopping lists
- **Family Journal**: Share updates and memories
- **AI Invitations**: Create event invitations with AI assistance

## Development

This project was generated with Angular CLI version 20.0.1.

### Development server

Run `ng serve` for a dev server. Navigate to `http://localhost:4200/`. The application will automatically reload if you change any of the source files.

### Build

Run `ng build` to build the project. The build artifacts will be stored in the `dist/` directory.

### Running unit tests

Run `ng test` to execute the unit tests via [Karma](https://karma-runner.github.io).

## Technologies Used

- Angular 20.0.0
- Tailwind CSS
- Font Awesome
- Google Fonts (Inter)
This project was generated using [Angular CLI](https://github.com/angular/angular-cli) version 20.0.1.

## Development server

To start a local development server, run:

```bash
ng serve
```

Once the server is running, open your browser and navigate to `http://localhost:4200/`. The application will automatically reload whenever you modify any of the source files.

The development server is configured with a proxy that routes all `/api` requests to `http://localhost:3000/api`. This allows the frontend to communicate with a backend API running on port 3000 during development.

## Code scaffolding

Angular CLI includes powerful code scaffolding tools. To generate a new component, run:

```bash
ng generate component component-name
```

For a complete list of available schematics (such as `components`, `directives`, or `pipes`), run:

```bash
ng generate --help
```

## Building
# Family Hub

A secure and private platform designed to help families stay connected, share memories, and coordinate activities together.

## Features

- **Family Calendar**: View and manage shared events
- **Media Sharing**: Share photos and videos with family members
- **File Vault**: Store important documents securely
- **Shared Lists**: Collaborate on to-do and shopping lists
- **Family Journal**: Share updates and memories
- **AI Invitations**: Create event invitations with AI assistance

## Development

This project was generated with Angular CLI version 20.0.1.

### Development server

Run `ng serve` for a dev server. Navigate to `http://localhost:4200/`. The application will automatically reload if you change any of the source files.

### Build

Run `ng build` to build the project. The build artifacts will be stored in the `dist/` directory.

### Running unit tests

Run `ng test` to execute the unit tests via [Karma](https://karma-runner.github.io).

## Technologies Used

- Angular 20.0.0
- Pure CSS (no external libraries)
- Font Awesome icons (CDN)
- Google Fonts (Inter)
To build the project run:

```bash
ng build
```

This will compile your project and store the build artifacts in the `dist/` directory. By default, the production build optimizes your application for performance and speed.

## Running unit tests

To execute unit tests with the [Karma](https://karma-runner.github.io) test runner, use the following command:

```bash
ng test
```

## Running end-to-end tests

For end-to-end (e2e) testing, run:

```bash
ng e2e
```

Angular CLI does not come with an end-to-end testing framework by default. You can choose one that suits your needs.

## Additional Resources

For more information on using the Angular CLI, including detailed command references, visit the [Angular CLI Overview and Command Reference](https://angular.dev/tools/cli) page.
